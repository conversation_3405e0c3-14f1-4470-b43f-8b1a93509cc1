/**
 * Hook para contar notificações não lidas
 * Inclui real-time updates via Supabase subscriptions
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/services/supabase/client';
import { NotificationService } from '@/services/notifications/core/notification-service';

interface UseNotificationCountOptions {
  userId: string;
  realTime?: boolean;
}

interface UseNotificationCountReturn {
  count: number;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useNotificationCount({
  userId,
  realTime = true
}: UseNotificationCountOptions): UseNotificationCountReturn {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const notificationService = new NotificationService();

  // Função para carregar o contador
  const loadCount = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await notificationService.getUnreadCount(userId);

      if (response.success && typeof response.data === 'number') {
        setCount(response.data);
      } else {
        setError(response.error || 'Erro ao carregar contador de notificações');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [userId, notificationService]);

  // Função para refresh manual
  const refresh = useCallback(async () => {
    await loadCount();
  }, [loadCount]);

  // Carregar contador inicial
  useEffect(() => {
    if (userId) {
      loadCount();
    }
  }, [userId, loadCount]);

  // Setup real-time subscription
  useEffect(() => {
    if (!realTime || !userId) return;

    const supabase = createClient();
    
    const subscription = supabase
      .channel('notification_count')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            // Nova notificação criada
            const newNotification = payload.new as any;
            if (newNotification.status === 'unread') {
              setCount(prev => prev + 1);
            }
          } else if (payload.eventType === 'UPDATE') {
            // Notificação atualizada
            const oldNotification = payload.old as any;
            const newNotification = payload.new as any;
            
            // Se mudou de unread para read/archived/deleted
            if (oldNotification.status === 'unread' && newNotification.status !== 'unread') {
              setCount(prev => Math.max(0, prev - 1));
            }
            // Se mudou de read/archived/deleted para unread
            else if (oldNotification.status !== 'unread' && newNotification.status === 'unread') {
              setCount(prev => prev + 1);
            }
          } else if (payload.eventType === 'DELETE') {
            // Notificação deletada
            const deletedNotification = payload.old as any;
            if (deletedNotification.status === 'unread') {
              setCount(prev => Math.max(0, prev - 1));
            }
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [realTime, userId]);

  return {
    count,
    loading,
    error,
    refresh
  };
}
