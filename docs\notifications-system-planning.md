# Sistema de Notificações - Planejamento Detalhado

  

## Visão Geral

  

Este documento detalha o planejamento para implementação de um sistema completo de notificações para o SaaS de gerenciamento de academias de artes marciais ApexDojo. O sistema suportará três canais principais:

  

1. **In-App** (Prioridade Alta) - Notificações dentro da plataforma

2. **E-mail** (Prioridade Média) - Resend → AWS SES

3. **WhatsApp** (Prioridade Baixa) - Evolution API

  

## Estado Atual da Aplicação

  

### Componentes Existentes

- ✅ `notifications-popover.tsx` - Componente de notificações no header (dados mockados)

- ✅ `notification-service.ts` - Serviço de notificações para billing (preparado para integração)

- ✅ `toast-notification.tsx` - Sistema de toasts temporários

- ❌ Tabelas de notificações no banco de dados

- ❌ Sistema de preferências de notificação

- ❌ Integração com provedores externos

  

### Arquitetura Multitenancy

- Cada academia (tenant) terá configurações independentes

- Domínios de e-mail: `<EMAIL>`

- **Instância única de WhatsApp** para todo o SaaS (compartilhada)

- Isolamento completo de dados entre tenants via RLS

  

## Fase 1: Sistema In-App (Prioridade Alta)

  

### 1.1 Estrutura do Banco de Dados

  

#### Tabela `notifications`

```sql

CREATE TABLE notifications (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  type VARCHAR(50) NOT NULL, -- 'payment', 'class', 'system', 'enrollment', 'event'

  category VARCHAR(50) NOT NULL, -- 'reminder', 'alert', 'info', 'success', 'error'

  priority VARCHAR(20) NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'

  title VARCHAR(255) NOT NULL,

  message TEXT NOT NULL,

  data JSONB DEFAULT '{}', -- Dados específicos da notificação

  status VARCHAR(20) NOT NULL DEFAULT 'unread', -- 'unread', 'read', 'archived', 'deleted'

  channels VARCHAR[] DEFAULT ARRAY['in_app'], -- Canais onde foi/será enviada

  scheduled_for TIMESTAMP WITH TIME ZONE, -- Para notificações agendadas

  expires_at TIMESTAMP WITH TIME ZONE, -- Data de expiração

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE,

  read_at TIMESTAMP WITH TIME ZONE,

  -- Índices para performance

  INDEX idx_notifications_tenant_user (tenant_id, user_id),

  INDEX idx_notifications_status (status),

  INDEX idx_notifications_type_category (type, category),

  INDEX idx_notifications_created_at (created_at DESC),

  INDEX idx_notifications_scheduled (scheduled_for) WHERE scheduled_for IS NOT NULL

);

```

  

#### Tabela `notification_preferences`

```sql

CREATE TABLE notification_preferences (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  notification_type VARCHAR(50) NOT NULL,

  in_app_enabled BOOLEAN DEFAULT true,

  email_enabled BOOLEAN DEFAULT true,

  whatsapp_enabled BOOLEAN DEFAULT false,

  quiet_hours_start TIME,

  quiet_hours_end TIME,

  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE,

  UNIQUE(tenant_id, user_id, notification_type)

);

```

  

#### Tabela `notification_templates`

```sql

CREATE TABLE notification_templates (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE, -- NULL para templates globais

  type VARCHAR(50) NOT NULL,

  channel VARCHAR(20) NOT NULL, -- 'in_app', 'email', 'whatsapp'

  name VARCHAR(100) NOT NULL,

  subject_template TEXT, -- Para e-mail

  body_template TEXT NOT NULL,

  variables JSONB DEFAULT '{}', -- Variáveis disponíveis no template

  is_active BOOLEAN DEFAULT true,

  is_default BOOLEAN DEFAULT false, -- Template padrão do sistema

  version INTEGER DEFAULT 1, -- Versionamento de templates

  parent_template_id UUID REFERENCES notification_templates(id), -- Referência ao template pai (para customizações)

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE,

  

  UNIQUE(tenant_id, type, channel, name)

);

```

  

#### Tabela `template_variables`

```sql

CREATE TABLE template_variables (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  template_type VARCHAR(50) NOT NULL, -- 'payment', 'class', 'system', etc.

  variable_name VARCHAR(100) NOT NULL,

  variable_key VARCHAR(100) NOT NULL, -- Chave usada no template (ex: {{academyName}})

  description TEXT,

  data_type VARCHAR(20) NOT NULL, -- 'string', 'number', 'date', 'boolean', 'url'

  is_required BOOLEAN DEFAULT false,

  default_value TEXT,

  example_value TEXT,

  category VARCHAR(50), -- 'academy', 'student', 'payment', 'class', etc.

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  

  UNIQUE(template_type, variable_key)

);

```

  

### 1.2 Tipos TypeScript

  

```typescript

// src/services/notifications/types/notification-types.ts

export interface Notification {

  id: string;

  tenant_id: string;

  user_id: string;

  type: NotificationType;

  category: NotificationCategory;

  priority: NotificationPriority;

  title: string;

  message: string;

  data: Record<string, any>;

  status: NotificationStatus;

  channels: NotificationChannel[];

  scheduled_for?: string;

  expires_at?: string;

  created_at: string;

  updated_at?: string;

  read_at?: string;

}

  

export type NotificationType =

  | 'payment'

  | 'class'

  | 'system'

  | 'enrollment'

  | 'event';

  

export type NotificationCategory =

  | 'reminder'

  | 'alert'

  | 'info'

  | 'success'

  | 'error';

  

export type NotificationPriority =

  | 'low'

  | 'medium'

  | 'high'

  | 'urgent';

  

export type NotificationStatus =

  | 'unread'

  | 'read'

  | 'archived'

  | 'deleted';

  

export type NotificationChannel =

  | 'in_app'

  | 'email'

  | 'whatsapp';

  

export interface NotificationPreferences {

  id: string;

  tenant_id: string;

  user_id: string;

  notification_type: NotificationType;

  in_app_enabled: boolean;

  email_enabled: boolean;

  whatsapp_enabled: boolean;

  quiet_hours_start?: string;

  quiet_hours_end?: string;

  timezone: string;

  created_at: string;

  updated_at?: string;

}

  

export interface CreateNotificationData {

  user_id: string;

  type: NotificationType;

  category: NotificationCategory;

  priority?: NotificationPriority;

  title: string;

  message: string;

  data?: Record<string, any>;

  channels?: NotificationChannel[];

  scheduled_for?: string;

  expires_at?: string;

}

  

export interface NotificationTemplate {

  id: string;

  tenant_id?: string;

  type: NotificationType;

  channel: NotificationChannel;

  name: string;

  subject_template?: string;

  body_template: string;

  variables: Record<string, any>;

  is_active: boolean;

  is_default: boolean;

  version: number;

  parent_template_id?: string;

  created_at: string;

  updated_at?: string;

}

  

export interface TemplateVariable {

  id: string;

  template_type: NotificationType;

  variable_name: string;

  variable_key: string;

  description?: string;

  data_type: 'string' | 'number' | 'date' | 'boolean' | 'url';

  is_required: boolean;

  default_value?: string;

  example_value?: string;

  category?: string;

  created_at: string;

}

  

export interface TemplatePreviewData {

  template: NotificationTemplate;

  variables: Record<string, any>;

  rendered_subject?: string;

  rendered_body: string;

}

```

  

### 1.3 Serviços Core

  

#### NotificationService Principal

```typescript

// src/services/notifications/core/notification-service.ts

export class NotificationService {

  async create(tenantId: string, data: CreateNotificationData): Promise<Notification>

  async getByUser(userId: string, filters?: NotificationFilters): Promise<PaginatedNotifications>

  async markAsRead(notificationId: string, userId: string): Promise<void>

  async markAllAsRead(userId: string): Promise<void>

  async archive(notificationId: string, userId: string): Promise<void>

  async delete(notificationId: string, userId: string): Promise<void>

  async getUnreadCount(userId: string): Promise<number>

  async scheduleNotification(tenantId: string, data: CreateNotificationData): Promise<void>

}

```

  

#### TemplateManagementService

```typescript

// src/services/notifications/templates/template-management-service.ts

export class TemplateManagementService {

  async getTemplatesByTenant(tenantId: string, type?: NotificationType): Promise<NotificationTemplate[]>

  async getTemplate(templateId: string): Promise<NotificationTemplate>

  async createTemplate(tenantId: string, template: CreateTemplateData): Promise<NotificationTemplate>

  async updateTemplate(templateId: string, updates: UpdateTemplateData): Promise<NotificationTemplate>

  async deleteTemplate(templateId: string): Promise<void>

  async duplicateTemplate(templateId: string, newName: string): Promise<NotificationTemplate>

  async resetToDefault(templateId: string): Promise<NotificationTemplate>

  async previewTemplate(templateId: string, variables: Record<string, any>): Promise<TemplatePreviewData>

  async getAvailableVariables(templateType: NotificationType): Promise<TemplateVariable[]>

  async validateTemplate(template: string, templateType: NotificationType): Promise<TemplateValidationResult>

  async getTemplateHistory(templateId: string): Promise<NotificationTemplate[]>

}

  

interface CreateTemplateData {

  type: NotificationType;

  channel: NotificationChannel;

  name: string;

  subject_template?: string;

  body_template: string;

  parent_template_id?: string;

}

  

interface UpdateTemplateData {

  name?: string;

  subject_template?: string;

  body_template?: string;

  is_active?: boolean;

}

  

interface TemplateValidationResult {

  isValid: boolean;

  errors: string[];

  warnings: string[];

  missingVariables: string[];

  unusedVariables: string[];

}

```

  

### 1.4 Hooks React

  

```typescript

// src/hooks/notifications/use-notifications.ts

export function useNotifications(filters?: NotificationFilters) {

  // Lista paginada de notificações

  // Real-time updates via Supabase subscriptions

}

  

export function useNotificationCount() {

  // Contador de notificações não lidas

  // Real-time updates

}

  

export function useNotificationPreferences() {

  // Gerenciamento de preferências do usuário

}

  

export function useTemplateManagement(tenantId: string) {

  // Gerenciamento de templates de notificação

  // CRUD operations, preview, validation

}

  

export function useTemplatePreview(templateId: string) {

  // Preview em tempo real de templates

  // Validação de sintaxe e variáveis

}

  

export function useTemplateVariables(templateType: NotificationType) {

  // Lista de variáveis disponíveis para um tipo de template

}

```

  

### 1.5 Componentes React

  

#### Lista de Notificações

```typescript

// src/components/notifications/notifications-list.tsx

export function NotificationsList({

  filters,

  onNotificationClick,

  showActions = true

}: NotificationsListProps)

```

  

#### Item de Notificação

```typescript

// src/components/notifications/notification-item.tsx

export function NotificationItem({

  notification,

  onClick,

  showActions = true

}: NotificationItemProps)

```

  

#### Componentes de Gerenciamento de Templates

```typescript

// src/components/notifications/templates/template-editor.tsx

export function TemplateEditor({

  template,

  onSave,

  onPreview,

  availableVariables

}: TemplateEditorProps) {

  // Editor WYSIWYG ou markdown para templates

  // Preview em tempo real

  // Lista de variáveis disponíveis

  // Validação de sintaxe

}

  

// src/components/notifications/templates/template-list.tsx

export function TemplateList({

  templates,

  onEdit,

  onDuplicate,

  onDelete,

  onResetToDefault

}: TemplateListProps) {

  // Lista de templates com ações

  // Filtros por tipo e canal

  // Status ativo/inativo

}

  

// src/components/notifications/templates/template-preview.tsx

export function TemplatePreview({

  template,

  variables,

  onVariableChange

}: TemplatePreviewProps) {

  // Preview do template renderizado

  // Formulário para testar variáveis

  // Visualização responsiva (desktop/mobile)

}

  

// src/components/notifications/templates/variable-selector.tsx

export function VariableSelector({

  availableVariables,

  onVariableSelect

}: VariableSelectorProps) {

  // Seletor de variáveis organizadas por categoria

  // Busca e filtros

  // Informações sobre cada variável

}

```

  

### 1.6 Páginas da Aplicação

  

#### Página de Notificações

```typescript

// src/app/(dashboard)/notificacoes/page.tsx

export default function NotificationsPage() {

  // Página completa com filtros, busca, ações em lote

  // Tabs: Todas, Não lidas, Arquivadas

  // Filtros: Tipo, Categoria, Período

}

```

  

#### Página de Gerenciamento de Templates

```typescript

// src/app/(dashboard)/configuracoes/templates/page.tsx

export default function TemplateManagementPage() {

  // Lista de templates por tipo de notificação

  // Ações: Criar, Editar, Duplicar, Excluir, Resetar

  // Filtros por canal (email, whatsapp, in-app)

  // Status ativo/inativo

}

  

// src/app/(dashboard)/configuracoes/templates/[id]/page.tsx

export default function TemplateEditorPage({ params }: { params: { id: string } }) {

  // Editor completo de template

  // Preview em tempo real

  // Lista de variáveis disponíveis

  // Histórico de versões

  // Validação e testes

}

  

// src/app/(dashboard)/configuracoes/templates/novo/page.tsx

export default function NewTemplatePage() {

  // Criação de novo template

  // Seleção de tipo e canal

  // Opção de partir de template padrão

}

```

  


## Fase 2: Sistema de E-mail (Prioridade Média)

  

### 2.1 Arquitetura de Provedores Centralizada

  

#### Interface Base

```typescript

// src/services/notifications/channels/email/providers/email-provider.ts

export interface EmailProvider {

  send(email: EmailData): Promise<EmailResult>;

  sendBatch(emails: EmailData[]): Promise<BatchEmailResult>;

  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;

  verifyDomain(domain: string): Promise<DomainVerification>;

}

```

  

#### Implementação Resend

```typescript

// src/services/notifications/channels/email/providers/resend-provider.ts

export class ResendProvider implements EmailProvider {

  constructor(private apiKey: string) {}

  async send(email: EmailData): Promise<EmailResult> {

    // Implementação com Resend SDK

  }

  // Outros métodos...

}

```

  

### 2.2 Sistema de Templates

  

#### Engine de Templates

```typescript

// src/services/notifications/templates/template-engine.ts

export class TemplateEngine {

  async renderEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail>

  async renderWhatsApp(templateId: string, variables: Record<string, any>): Promise<string>

}

```

  

#### Templates React Email com Logo da Academia

```typescript

// src/services/notifications/channels/email/templates/payment-reminder.tsx

export function PaymentReminderTemplate({

  studentName,

  amount,

  dueDate,

  academyName,

  academyLogo, // URL da logo da academia (vem de tenants.logo_url)

  academyColors // Cores da academia (primary_color, secondary_color)

}: PaymentReminderProps) {

  return (

    <Html>

      <Head />

      <Body style={{ fontFamily: 'Arial, sans-serif' }}>

        <Container style={{ maxWidth: '600px', margin: '0 auto' }}>

          {/* Header com logo da academia */}

          <Section style={{ textAlign: 'center', padding: '20px 0' }}>

            {academyLogo && (

              <Img

                src={academyLogo}

                alt={`Logo ${academyName}`}

                style={{ maxHeight: '80px', maxWidth: '200px' }}

              />

            )}

            <Heading style={{ color: academyColors?.primary_color || '#333' }}>

              {academyName}

            </Heading>

          </Section>

  

          {/* Conteúdo personalizável */}

          <Section style={{ padding: '20px' }}>

            <Text>Olá {studentName},</Text>

            <Text>

              Lembramos que você possui uma mensalidade no valor de

              <strong>R$ {amount}</strong> com vencimento em <strong>{dueDate}</strong>.

            </Text>

            {/* Resto do template... */}

          </Section>

  

          {/* Footer com dados da academia */}

          <Section style={{

            borderTop: `2px solid ${academyColors?.primary_color || '#333'}`,

            padding: '20px',

            textAlign: 'center',

            fontSize: '12px',

            color: '#666'

          }}>

            <Text>{academyName}</Text>

          </Section>

        </Container>

      </Body>

    </Html>

  );

}

  

interface PaymentReminderProps {

  studentName: string;

  amount: number;

  dueDate: string;

  academyName: string;

  academyLogo?: string;

  academyColors?: {

    primary_color?: string;

    secondary_color?: string;

  };

}

```

  

### 2.3 Configuração Centralizada por Tenant

  

O SaaS gerencia todas as chaves de API centralmente via variáveis de ambiente. Cada tenant configura apenas preferências de apresentação:

  

```typescript

// Serviço de configuração centralizada

export class NotificationConfigService {

  // Configurações do SaaS (vêm do ambiente)

  private static getSaaSConfig(): SaaSNotificationConfig {

    return {

      email: {

        provider: process.env.EMAIL_PROVIDER as 'resend' | 'aws_ses',

        resend: {

          apiKey: process.env.RESEND_API_KEY!,

          fromDomain: process.env.RESEND_FROM_DOMAIN!,

          webhookSecret: process.env.RESEND_WEBHOOK_SECRET!

        },

        aws: {

          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,

          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,

          region: process.env.AWS_REGION!,

          fromDomain: process.env.AWS_SES_FROM_DOMAIN!

        }

      },

      whatsapp: {

        apiUrl: process.env.EVOLUTION_API_URL!,

        apiKey: process.env.EVOLUTION_API_KEY!,

        instanceName: process.env.EVOLUTION_INSTANCE_NAME!,

        webhookSecret: process.env.EVOLUTION_WEBHOOK_SECRET!

      }

    };

  }

  

  // Configurações específicas do tenant (vêm do banco)

  async getTenantConfig(tenantId: string): Promise<TenantNotificationConfig> {

    const supabase = await createClient();

  

    const { data: tenant } = await supabase

      .from('tenants')

      .select('name, slug')

      .eq('id', tenantId)

      .single();

  

    const { data: settings } = await supabase

      .from('tenant_notification_settings')

      .select('*')

      .eq('tenant_id', tenantId)

      .single();

  

    return {

      tenantId,

      tenantName: tenant?.name || 'Academia',

      tenantSlug: tenant?.slug || 'academia',

      emailFromDomain: `${tenant?.slug || 'academia'}@meusaas.com`,

      emailFromName: settings?.email_from_name || tenant?.name || 'Academia',

      emailEnabled: settings?.email_enabled ?? true,

      whatsappEnabled: settings?.whatsapp_enabled ?? true,

      whatsappOptInMessage: settings?.whatsapp_opt_in_message,

      notificationTypes: settings?.notification_types || {},

      quietHours: {

        start: settings?.quiet_hours_start || '22:00:00',

        end: settings?.quiet_hours_end || '08:00:00',

        timezone: settings?.timezone || 'America/Sao_Paulo'

      }

    };

  }

}

```

  

### 2.4 Estrutura do Banco Atualizada

  

```sql

-- Configurações de notificação por tenant (sem chaves de API)

CREATE TABLE tenant_notification_settings (

  tenant_id UUID PRIMARY KEY REFERENCES tenants(id) ON DELETE CASCADE,

  email_from_domain VARCHAR(100), -- '<EMAIL>' (gerado automaticamente)

  email_from_name VARCHAR(100), -- Nome que aparece no remetente

  email_enabled BOOLEAN DEFAULT true, -- Se a academia usa notificações por email

  whatsapp_enabled BOOLEAN DEFAULT true, -- Se a academia usa WhatsApp

  whatsapp_opt_in_message TEXT, -- Mensagem personalizada de opt-in

  notification_types JSONB DEFAULT '{}', -- Tipos de notificação habilitados por canal

  quiet_hours_start TIME DEFAULT '22:00:00', -- Horário de silêncio início

  quiet_hours_end TIME DEFAULT '08:00:00', -- Horário de silêncio fim

  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE

);

  

-- Instância única do SaaS para WhatsApp (SIMPLIFICADA)

CREATE TABLE whatsapp_saas_instance (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  instance_name VARCHAR(100) NOT NULL UNIQUE DEFAULT 'apexsaas-whatsapp',

  instance_key VARCHAR(255) NOT NULL,

  status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'disconnected', 'error'

  webhook_url TEXT,

  last_seen TIMESTAMP WITH TIME ZONE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE

);

  

-- Contatos WhatsApp (OTIMIZADA)

CREATE TABLE whatsapp_contacts (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  opted_in BOOLEAN DEFAULT false,

  opted_in_at TIMESTAMP WITH TIME ZONE,

  opted_out_at TIMESTAMP WITH TIME ZONE,

  opt_out_reason VARCHAR(100),

  last_message_at TIMESTAMP WITH TIME ZONE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE,

  

  UNIQUE(tenant_id, user_id)

);

```

  

## Fase 3: Sistema WhatsApp (Prioridade Baixa)

  

### 3.1 Integração Evolution API

  

#### Cliente Evolution API

```typescript

// src/services/notifications/channels/whatsapp/evolution-api-client.ts

export class EvolutionApiClient {

  async sendMessage(instanceId: string, message: WhatsAppMessage): Promise<MessageResult>

  async getInstanceStatus(instanceId: string): Promise<InstanceStatus>

  async validateNumber(number: string): Promise<NumberValidation>

  async setupWebhook(instanceId: string, webhookUrl: string): Promise<void>

}

```

  

### 3.2 Webhook Handling

  

```typescript

// src/app/api/webhooks/whatsapp/route.ts

export async function POST(request: Request) {

  // Processar webhooks da Evolution API

  // Status de entrega, leitura, etc.

}

```

  

### 3.3 Compliance e Opt-in

  

```sql

// Tabela whatsapp_contacts

CREATE TABLE whatsapp_contacts (

  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  opted_in BOOLEAN DEFAULT false,

  opted_in_at TIMESTAMP WITH TIME ZONE,

  opted_out_at TIMESTAMP WITH TIME ZONE,

  opt_out_reason VARCHAR(100),

  last_message_at TIMESTAMP WITH TIME ZONE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  updated_at TIMESTAMP WITH TIME ZONE,

  

  UNIQUE(tenant_id, user_id)

);

```

  

## Estrutura de Arquivos

  

```

src/services/notifications/

├── core/

│   ├── notification-service.ts

│   ├── notification-queue.ts

│   └── notification-analytics.ts

├── channels/

│   ├── base/

│   │   └── notification-channel.ts

│   ├── in-app/

│   │   ├── in-app-channel.ts

│   │   └── in-app-service.ts

│   ├── email/

│   │   ├── email-channel.ts

│   │   ├── providers/

│   │   │   ├── email-provider.ts

│   │   │   ├── resend-provider.ts

│   │   │   └── aws-provider.ts

│   │   └── templates/

│   └── whatsapp/

│       ├── whatsapp-channel.ts

│       ├── evolution-api-client.ts

│       └── templates/

├── templates/

│   ├── template-engine.ts

│   ├── template-management-service.ts

│   ├── template-validator.ts

│   └── types.ts

├── types/

│   └── notification-types.ts

└── index.ts

  

src/components/notifications/

├── notifications-list.tsx

├── notification-item.tsx

├── notification-preferences.tsx

├── notification-analytics.tsx

└── templates/

    ├── template-editor.tsx

    ├── template-list.tsx

    ├── template-preview.tsx

    ├── variable-selector.tsx

    └── template-history.tsx

  

src/hooks/notifications/

├── use-notifications.ts

├── use-notification-count.ts

├── use-notification-preferences.ts

├── use-template-management.ts

├── use-template-preview.ts

└── use-template-variables.ts

  

src/app/(dashboard)/configuracoes/templates/

├── page.tsx                    # Lista de templates

├── novo/

│   └── page.tsx               # Criar novo template

└── [id]/

    ├── page.tsx               # Editor de template

    └── historico/

        └── page.tsx           # Histórico de versões

```

  

## Configuração de Ambiente

### Variáveis de Ambiente Necessárias

  

```env

# Configurações de E-mail (Resend)

RESEND_API_KEY=re_xxxxxxxxx

RESEND_FROM_DOMAIN=meusaas.com

RESEND_WEBHOOK_SECRET=whsec_xxxxxxxxx

  

# Configurações de E-mail (AWS SES - Futuro)

AWS_ACCESS_KEY_ID=AKIA...

AWS_SECRET_ACCESS_KEY=...

AWS_REGION=us-east-1

AWS_SES_FROM_DOMAIN=meusaas.com

  

# Configurações WhatsApp (Evolution API)

EVOLUTION_API_URL=https://your-evolution-api.com

EVOLUTION_API_KEY=your_global_api_key

EVOLUTION_WEBHOOK_SECRET=your_webhook_secret

EVOLUTION_INSTANCE_NAME=apexdojo-whatsapp

  

# Configurações Gerais

EMAIL_PROVIDER=resend # 'resend' ou 'aws_ses'

EMAIL_FROM_NAME="ApexDojo SaaS"

EMAIL_REPLY_TO=<EMAIL>

NOTIFICATIONS_ENABLED=true

```

  

## Cronograma de Implementação

### Sprint 1 (1-2 semanas) - Base In-App

- [x] Criação das tabelas no banco

- [x] Tipos TypeScript e schemas Zod

- [x] NotificationService básico

- [x] Hooks React básicos

  

### Sprint 2 (1-2 semanas) - Interface In-App

- [ ] Atualização do notifications-popover

- [ ] Página de notificações

- [ ] Sistema de preferências

- [ ] Real-time updates

  

### Sprint 2.5 (1-2 semanas) - Sistema de Templates Personalizáveis

- [ ] Criação da tabela `template_variables` com dados iniciais

- [ ] TemplateManagementService completo

- [ ] Hooks para gerenciamento de templates

- [ ] Componentes de edição de templates

- [ ] Página de gerenciamento de templates

- [ ] Editor com preview em tempo real

- [ ] Sistema de variáveis (incluindo logo da academia)

- [ ] Validação de templates

- [ ] Templates padrão do sistema

  

### Sprint 3 (2-3 semanas) - Sistema E-mail

- [ ] Abstração de provedores

- [ ] Implementação Resend

- [ ] Templates React Email

- [ ] Configuração por tenant

  

### Sprint 4 (2-3 semanas) - Sistema WhatsApp

- [ ] Setup instância única Evolution API

- [ ] Integração com instância centralizada

- [ ] Templates WhatsApp com assinatura da academia

- [ ] Sistema de opt-in/opt-out global

- [ ] Webhook handling centralizado

  

### Sprint 5 (1 semana) - Polimento

- [ ] Testes completos

- [ ] Documentação

- [ ] Analytics e monitoramento

- [ ] Performance optimization

  

## Funcionalidades de Templates Personalizáveis

  

### 🎨 **Interface de Gerenciamento**

- **Lista de Templates**: Visualização organizada por tipo de notificação

- **Editor WYSIWYG**: Interface amigável para edição de templates

- **Preview em Tempo Real**: Visualização instantânea das alterações

- **Histórico de Versões**: Controle de mudanças e possibilidade de rollback

  

### 🔧 **Sistema de Variáveis**

- **Variáveis da Academia**: Nome, logo, cores, informações de contato

- **Variáveis Específicas**: Dados do estudante, pagamento, aula, etc.

- **Validação Automática**: Verificação de variáveis obrigatórias e sintaxe

- **Documentação Integrada**: Descrição e exemplos de cada variável

  

### 🎯 **Personalização Avançada**

- **Templates por Tipo**: Cobrança, Aula, Sistema, Eventos, Matrículas

- **Multi-canal**: Templates específicos para email, WhatsApp e in-app

- **Cores da Academia**: Integração automática com as cores do tenant

- **Logo Responsiva**: Adaptação automática da logo para diferentes dispositivos

  

### 📊 **Dados das Variáveis Disponíveis**

  

#### Variáveis da Academia (sempre disponíveis)

```typescript

interface AcademyVariables {

  academyName: string;        // Nome da academia

  academyLogo: string;        // URL da logo (tenants.logo_url)

  academySlug: string;        // Slug da academia

  primaryColor: string;       // Cor primária (tenants.primary_color)

  secondaryColor: string;     // Cor secundária (tenants.secondary_color)

  academyEmail: string;       // Email da academia

  academyPhone: string;       // Telefone da academia

}

```

  

#### Variáveis por Tipo de Notificação

  

**Payment (Cobrança)**

```typescript

interface PaymentVariables extends AcademyVariables {

  studentName: string;        // Nome do estudante

  amount: number;            // Valor da mensalidade

  dueDate: string;           // Data de vencimento

  planName: string;          // Nome do plano

  paymentMethod: string;     // Método de pagamento

  invoiceUrl: string;        // URL da fatura

}

```

  

**Class (Aula)**

```typescript

interface ClassVariables extends AcademyVariables {

  studentName: string;       // Nome do estudante

  className: string;         // Nome da aula

  instructorName: string;    // Nome do instrutor

  classDate: string;         // Data da aula

  classTime: string;         // Horário da aula

  location: string;          // Local da aula

}

```

  

**Enrollment (Matrícula)**

```typescript

interface EnrollmentVariables extends AcademyVariables {

  studentName: string;       // Nome do estudante

  planName: string;          // Nome do plano

  startDate: string;         // Data de início

  welcomeMessage: string;    // Mensagem de boas-vindas

}

```

  

## Benefícios da Configuração Centralizada

  

### 🔐 **Segurança**

- **Chaves protegidas**: APIs keys ficam apenas no servidor, nunca expostas

- **Controle centralizado**: Administração única de credenciais

- **Rotação simplificada**: Mudança de chaves em um só lugar

- **Auditoria**: Logs centralizados de uso das APIs

  

### 💰 **Economia**

- **Contas unificadas**: Uma conta Resend/Evolution para todo o SaaS

- **Negociação de volume**: Melhores preços por volume total

- **Gestão simplificada**: Menos contas para gerenciar

- **Previsibilidade**: Custos centralizados e controláveis

  

### 🚀 **Escalabilidade**

- **Limites globais**: Rate limiting inteligente entre tenants

- **Balanceamento**: Distribuição otimizada de recursos

- **Monitoramento**: Métricas centralizadas de performance

- **Manutenção**: Updates e patches em um só lugar

  

### 👥 **Experiência do Tenant**

- **Configuração simples**: Apenas preferências de apresentação

- **Onboarding rápido**: Notificações funcionam imediatamente

- **Personalização**: Nome e domínio personalizados automaticamente

- **Confiabilidade**: Infraestrutura gerenciada pelo SaaS

  

## Considerações Técnicas

  

### Performance

- Paginação para listas grandes

- Cache Redis para notificações frequentes

- Índices otimizados no banco

- Lazy loading de componentes

  

### Segurança

- RLS no Supabase para isolamento

- Validação de permissões

- Sanitização de templates

- Rate limiting por tenant

- **Chaves de API protegidas no ambiente**

  

### Escalabilidade

- Processamento assíncrono

- Filas para envios em lote

- Retry logic para falhas

- Monitoramento de performance

- **Configuração centralizada de provedores**

  

### Experiência do Usuário

- Real-time updates

- Modo offline com sincronização

- Acessibilidade completa

- Interface responsiva

  

## Dados Iniciais para Template Variables

  

### Script de População da Tabela `template_variables`

```sql

-- Variáveis da Academia (disponíveis em todos os templates)

INSERT INTO template_variables (template_type, variable_name, variable_key, description, data_type, is_required, category, example_value) VALUES

('payment', 'Nome da Academia', 'academyName', 'Nome da academia/dojo', 'string', true, 'academy', 'Dojo Samurai'),

('payment', 'Logo da Academia', 'academyLogo', 'URL da logo da academia', 'url', false, 'academy', 'https://exemplo.com/logo.png'),

('payment', 'Cor Primária', 'primaryColor', 'Cor primária da academia', 'string', false, 'academy', '#FF6B35'),

('payment', 'Cor Secundária', 'secondaryColor', 'Cor secundária da academia', 'string', false, 'academy', '#004E89'),

  

-- Variáveis específicas de Payment

('payment', 'Nome do Estudante', 'studentName', 'Nome completo do estudante', 'string', true, 'student', 'João Silva'),

('payment', 'Valor', 'amount', 'Valor da mensalidade', 'number', true, 'payment', '150.00'),

('payment', 'Data de Vencimento', 'dueDate', 'Data de vencimento da mensalidade', 'date', true, 'payment', '2025-02-15'),

('payment', 'Nome do Plano', 'planName', 'Nome do plano contratado', 'string', true, 'payment', 'Plano Mensal Jiu-Jitsu'),

  

-- Variáveis específicas de Class

('class', 'Nome da Academia', 'academyName', 'Nome da academia/dojo', 'string', true, 'academy', 'Dojo Samurai'),

('class', 'Logo da Academia', 'academyLogo', 'URL da logo da academia', 'url', false, 'academy', 'https://exemplo.com/logo.png'),

('class', 'Nome do Estudante', 'studentName', 'Nome completo do estudante', 'string', true, 'student', 'João Silva'),

('class', 'Nome da Aula', 'className', 'Nome/tipo da aula', 'string', true, 'class', 'Jiu-Jitsu Iniciante'),

('class', 'Instrutor', 'instructorName', 'Nome do instrutor', 'string', true, 'class', 'Sensei Carlos'),

('class', 'Data da Aula', 'classDate', 'Data da aula', 'date', true, 'class', '2025-02-15'),

('class', 'Horário', 'classTime', 'Horário da aula', 'string', true, 'class', '19:00'),

  

-- Variáveis específicas de Enrollment

('enrollment', 'Nome da Academia', 'academyName', 'Nome da academia/dojo', 'string', true, 'academy', 'Dojo Samurai'),

('enrollment', 'Logo da Academia', 'academyLogo', 'URL da logo da academia', 'url', false, 'academy', 'https://exemplo.com/logo.png'),

('enrollment', 'Nome do Estudante', 'studentName', 'Nome completo do estudante', 'string', true, 'student', 'João Silva'),

('enrollment', 'Nome do Plano', 'planName', 'Nome do plano contratado', 'string', true, 'enrollment', 'Plano Mensal Jiu-Jitsu'),

('enrollment', 'Data de Início', 'startDate', 'Data de início das atividades', 'date', true, 'enrollment', '2025-02-01'),

  

-- Variáveis específicas de System

('system', 'Nome da Academia', 'academyName', 'Nome da academia/dojo', 'string', true, 'academy', 'Dojo Samurai'),

('system', 'Logo da Academia', 'academyLogo', 'URL da logo da academia', 'url', false, 'academy', 'https://exemplo.com/logo.png'),

('system', 'Nome do Usuário', 'userName', 'Nome do usuário', 'string', true, 'user', 'João Silva'),

('system', 'Mensagem', 'message', 'Mensagem do sistema', 'string', true, 'system', 'Sistema será atualizado hoje às 22h'),

  

-- Variáveis específicas de Event

('event', 'Nome da Academia', 'academyName', 'Nome da academia/dojo', 'string', true, 'academy', 'Dojo Samurai'),

('event', 'Logo da Academia', 'academyLogo', 'URL da logo da academia', 'url', false, 'academy', 'https://exemplo.com/logo.png'),

('event', 'Nome do Evento', 'eventName', 'Nome do evento', 'string', true, 'event', 'Campeonato Interno'),

('event', 'Data do Evento', 'eventDate', 'Data do evento', 'date', true, 'event', '2025-03-15'),

('event', 'Local', 'eventLocation', 'Local do evento', 'string', true, 'event', 'Academia Principal'),

('event', 'Descrição', 'eventDescription', 'Descrição do evento', 'string', false, 'event', 'Campeonato interno de Jiu-Jitsu para todas as graduações');

```

  

## Próximos Passos

  

1. **Aprovação do Planejamento** - Revisar e aprovar este documento

2. **Setup do Ambiente** - Configurar dependências (Resend, etc.)

3. **Implementação Fase 1** - Começar com sistema In-App

4. **Implementação Templates** - Sistema de templates personalizáveis

5. **Testes e Validação** - Testar cada fase antes de prosseguir

6. **Deploy Gradual** - Implementar feature flags para rollout controlado

  

---

  

**Documento criado em:** 2025-01-26  

**Versão:** 1.0  

**Status:** Aguardando Aprovação