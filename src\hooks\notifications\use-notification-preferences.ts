/**
 * Hook para gerenciar preferências de notificação do usuário
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/services/supabase/client';
import type {
  NotificationPreferences,
  NotificationType,
  CreateNotificationPreferencesData,
  UpdateNotificationPreferencesData,
  NotificationServiceResponse
} from '@/services/notifications/types/notification-types';
import {
  CreateNotificationPreferencesSchema,
  UpdateNotificationPreferencesSchema
} from '@/services/notifications/types/notification-schemas';

interface UseNotificationPreferencesOptions {
  userId: string;
  tenantId: string;
}

interface UseNotificationPreferencesReturn {
  preferences: Record<NotificationType, NotificationPreferences>;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  updatePreferences: (type: NotificationType, data: UpdateNotificationPreferencesData) => Promise<void>;
  createPreferences: (data: CreateNotificationPreferencesData) => Promise<void>;
}

export function useNotificationPreferences({
  userId,
  tenantId
}: UseNotificationPreferencesOptions): UseNotificationPreferencesReturn {
  const [preferences, setPreferences] = useState<Record<NotificationType, NotificationPreferences>>({} as any);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar preferências
  const loadPreferences = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClient();
      
      const { data: preferencesData, error: fetchError } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .eq('tenant_id', tenantId);

      if (fetchError) {
        setError(`Erro ao carregar preferências: ${fetchError.message}`);
        return;
      }

      // Organizar preferências por tipo
      const preferencesMap: Record<NotificationType, NotificationPreferences> = {} as any;
      
      preferencesData?.forEach(pref => {
        preferencesMap[pref.notification_type as NotificationType] = pref;
      });

      setPreferences(preferencesMap);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [userId, tenantId]);

  // Função para criar preferências
  const createPreferences = useCallback(async (data: CreateNotificationPreferencesData) => {
    try {
      setError(null);

      // Validar dados
      const validatedData = CreateNotificationPreferencesSchema.parse(data);

      const supabase = createClient();
      
      const { data: newPreferences, error: createError } = await supabase
        .from('notification_preferences')
        .insert({
          tenant_id: tenantId,
          user_id: userId,
          ...validatedData
        })
        .select()
        .single();

      if (createError) {
        setError(`Erro ao criar preferências: ${createError.message}`);
        return;
      }

      // Atualizar estado local
      setPreferences(prev => ({
        ...prev,
        [newPreferences.notification_type]: newPreferences
      }));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId, tenantId]);

  // Função para atualizar preferências
  const updatePreferences = useCallback(async (type: NotificationType, data: UpdateNotificationPreferencesData) => {
    try {
      setError(null);

      // Validar dados
      const validatedData = UpdateNotificationPreferencesSchema.parse(data);

      const supabase = createClient();
      
      const { data: updatedPreferences, error: updateError } = await supabase
        .from('notification_preferences')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .eq('notification_type', type)
        .select()
        .single();

      if (updateError) {
        setError(`Erro ao atualizar preferências: ${updateError.message}`);
        return;
      }

      // Atualizar estado local
      setPreferences(prev => ({
        ...prev,
        [type]: updatedPreferences
      }));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId, tenantId]);

  // Função para refresh manual
  const refresh = useCallback(async () => {
    await loadPreferences();
  }, [loadPreferences]);

  // Carregar preferências iniciais
  useEffect(() => {
    if (userId && tenantId) {
      loadPreferences();
    }
  }, [userId, tenantId, loadPreferences]);

  return {
    preferences,
    loading,
    error,
    refresh,
    updatePreferences,
    createPreferences
  };
}
